import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { adminDb } from './firebase-admin';
import { 
  Member, 
  Task, 
  FollowUp, 
  User,
  InsertMember, 
  InsertTask, 
  InsertFollowUp, 
  InsertUser,
  COLLECTIONS,
  memberSchema,
  taskSchema,
  followUpSchema,
  userSchema,
  timestampToDate
} from '@shared/firestore-schema';

// Helper function to convert Firestore document to typed object
const convertFirestoreDoc = <T>(doc: QueryDocumentSnapshot<DocumentData>, schema: any): T => {
  const data = doc.data();
  const converted = {
    id: doc.id,
    ...data,
    // Convert Firestore Timestamps to Dates
    ...(data.createdAt && { createdAt: timestampToDate(data.createdAt) }),
    ...(data.updatedAt && { updatedAt: timestampToDate(data.updatedAt) }),
    ...(data.convertedDate && { convertedDate: timestampToDate(data.convertedDate) }),
    ...(data.baptismDate && { baptismDate: timestampToDate(data.baptismDate) }),
    ...(data.dueDate && { dueDate: timestampToDate(data.dueDate) }),
    ...(data.completedDate && { completedDate: timestampToDate(data.completedDate) }),
    ...(data.scheduledDate && { scheduledDate: timestampToDate(data.scheduledDate) }),
    ...(data.nextFollowUp && { nextFollowUp: timestampToDate(data.nextFollowUp) }),
  };
  
  return schema.parse(converted) as T;
};

// Helper function to prepare data for Firestore (convert Dates to Timestamps)
const prepareForFirestore = (data: any) => {
  const prepared = { ...data };
  
  // Convert Date objects to Firestore Timestamps
  Object.keys(prepared).forEach(key => {
    if (prepared[key] instanceof Date) {
      prepared[key] = Timestamp.fromDate(prepared[key]);
    }
  });
  
  // Add timestamps
  const now = Timestamp.now();
  if (!prepared.createdAt) prepared.createdAt = now;
  prepared.updatedAt = now;
  
  return prepared;
};

export class FirestoreStorage {
  // Member operations
  async getMembers(): Promise<Member[]> {
    const membersRef = collection(adminDb, COLLECTIONS.MEMBERS);
    const snapshot = await getDocs(query(membersRef, orderBy('createdAt', 'desc')));
    return snapshot.docs.map(doc => convertFirestoreDoc<Member>(doc, memberSchema));
  }

  async getMember(id: string): Promise<Member | null> {
    const memberRef = doc(adminDb, COLLECTIONS.MEMBERS, id);
    const snapshot = await getDoc(memberRef);
    
    if (!snapshot.exists()) {
      return null;
    }
    
    return convertFirestoreDoc<Member>(snapshot as QueryDocumentSnapshot<DocumentData>, memberSchema);
  }

  async createMember(memberData: InsertMember): Promise<Member> {
    const membersRef = collection(adminDb, COLLECTIONS.MEMBERS);
    const preparedData = prepareForFirestore(memberData);
    const docRef = await addDoc(membersRef, preparedData);
    
    const newMember = await this.getMember(docRef.id);
    if (!newMember) {
      throw new Error('Failed to create member');
    }
    
    return newMember;
  }

  async updateMember(id: string, memberData: Partial<InsertMember>): Promise<Member> {
    const memberRef = doc(adminDb, COLLECTIONS.MEMBERS, id);
    const preparedData = prepareForFirestore(memberData);
    await updateDoc(memberRef, preparedData);
    
    const updatedMember = await this.getMember(id);
    if (!updatedMember) {
      throw new Error('Failed to update member');
    }
    
    return updatedMember;
  }

  async deleteMember(id: string): Promise<void> {
    const memberRef = doc(adminDb, COLLECTIONS.MEMBERS, id);
    await deleteDoc(memberRef);
  }

  async getRecentMembers(limitCount: number = 5): Promise<Member[]> {
    const membersRef = collection(adminDb, COLLECTIONS.MEMBERS);
    const snapshot = await getDocs(
      query(membersRef, orderBy('createdAt', 'desc'), limit(limitCount))
    );
    return snapshot.docs.map(doc => convertFirestoreDoc<Member>(doc, memberSchema));
  }

  // Task operations
  async getTasks(): Promise<Task[]> {
    const tasksRef = collection(adminDb, COLLECTIONS.TASKS);
    const snapshot = await getDocs(query(tasksRef, orderBy('dueDate', 'asc')));
    return snapshot.docs.map(doc => convertFirestoreDoc<Task>(doc, taskSchema));
  }

  async getTask(id: string): Promise<Task | null> {
    const taskRef = doc(adminDb, COLLECTIONS.TASKS, id);
    const snapshot = await getDoc(taskRef);
    
    if (!snapshot.exists()) {
      return null;
    }
    
    return convertFirestoreDoc<Task>(snapshot as QueryDocumentSnapshot<DocumentData>, taskSchema);
  }

  async createTask(taskData: InsertTask): Promise<Task> {
    const tasksRef = collection(adminDb, COLLECTIONS.TASKS);
    const preparedData = prepareForFirestore(taskData);
    const docRef = await addDoc(tasksRef, preparedData);
    
    const newTask = await this.getTask(docRef.id);
    if (!newTask) {
      throw new Error('Failed to create task');
    }
    
    return newTask;
  }

  async updateTask(id: string, taskData: Partial<InsertTask>): Promise<Task> {
    const taskRef = doc(adminDb, COLLECTIONS.TASKS, id);
    const preparedData = prepareForFirestore(taskData);
    await updateDoc(taskRef, preparedData);
    
    const updatedTask = await this.getTask(id);
    if (!updatedTask) {
      throw new Error('Failed to update task');
    }
    
    return updatedTask;
  }

  async deleteTask(id: string): Promise<void> {
    const taskRef = doc(adminDb, COLLECTIONS.TASKS, id);
    await deleteDoc(taskRef);
  }

  async getTasksByMember(memberId: string): Promise<Task[]> {
    const tasksRef = collection(adminDb, COLLECTIONS.TASKS);
    const snapshot = await getDocs(
      query(tasksRef, where('memberId', '==', memberId), orderBy('dueDate', 'asc'))
    );
    return snapshot.docs.map(doc => convertFirestoreDoc<Task>(doc, taskSchema));
  }

  async getPendingTasks(): Promise<Task[]> {
    const tasksRef = collection(adminDb, COLLECTIONS.TASKS);
    const snapshot = await getDocs(
      query(tasksRef, where('status', '==', 'pending'), orderBy('dueDate', 'asc'))
    );
    return snapshot.docs.map(doc => convertFirestoreDoc<Task>(doc, taskSchema));
  }

  // FollowUp operations
  async getFollowUps(): Promise<FollowUp[]> {
    const followUpsRef = collection(adminDb, COLLECTIONS.FOLLOW_UPS);
    const snapshot = await getDocs(query(followUpsRef, orderBy('scheduledDate', 'asc')));
    return snapshot.docs.map(doc => convertFirestoreDoc<FollowUp>(doc, followUpSchema));
  }

  async getFollowUp(id: string): Promise<FollowUp | null> {
    const followUpRef = doc(adminDb, COLLECTIONS.FOLLOW_UPS, id);
    const snapshot = await getDoc(followUpRef);
    
    if (!snapshot.exists()) {
      return null;
    }
    
    return convertFirestoreDoc<FollowUp>(snapshot as QueryDocumentSnapshot<DocumentData>, followUpSchema);
  }

  async createFollowUp(followUpData: InsertFollowUp): Promise<FollowUp> {
    const followUpsRef = collection(adminDb, COLLECTIONS.FOLLOW_UPS);
    const preparedData = prepareForFirestore(followUpData);
    const docRef = await addDoc(followUpsRef, preparedData);
    
    const newFollowUp = await this.getFollowUp(docRef.id);
    if (!newFollowUp) {
      throw new Error('Failed to create follow-up');
    }
    
    return newFollowUp;
  }

  async updateFollowUp(id: string, followUpData: Partial<InsertFollowUp>): Promise<FollowUp> {
    const followUpRef = doc(adminDb, COLLECTIONS.FOLLOW_UPS, id);
    const preparedData = prepareForFirestore(followUpData);
    await updateDoc(followUpRef, preparedData);
    
    const updatedFollowUp = await this.getFollowUp(id);
    if (!updatedFollowUp) {
      throw new Error('Failed to update follow-up');
    }
    
    return updatedFollowUp;
  }

  async deleteFollowUp(id: string): Promise<void> {
    const followUpRef = doc(adminDb, COLLECTIONS.FOLLOW_UPS, id);
    await deleteDoc(followUpRef);
  }

  async getFollowUpsByMember(memberId: string): Promise<FollowUp[]> {
    const followUpsRef = collection(adminDb, COLLECTIONS.FOLLOW_UPS);
    const snapshot = await getDocs(
      query(followUpsRef, where('memberId', '==', memberId), orderBy('scheduledDate', 'asc'))
    );
    return snapshot.docs.map(doc => convertFirestoreDoc<FollowUp>(doc, followUpSchema));
  }

  // User operations
  async getUser(id: string): Promise<User | null> {
    const userRef = doc(adminDb, COLLECTIONS.USERS, id);
    const snapshot = await getDoc(userRef);
    
    if (!snapshot.exists()) {
      return null;
    }
    
    return convertFirestoreDoc<User>(snapshot as QueryDocumentSnapshot<DocumentData>, userSchema);
  }

  async createUser(userData: InsertUser & { id: string }): Promise<User> {
    const userRef = doc(adminDb, COLLECTIONS.USERS, userData.id);
    const preparedData = prepareForFirestore(userData);
    await updateDoc(userRef, preparedData);
    
    const newUser = await this.getUser(userData.id);
    if (!newUser) {
      throw new Error('Failed to create user');
    }
    
    return newUser;
  }

  async updateUser(id: string, userData: Partial<InsertUser>): Promise<User> {
    const userRef = doc(adminDb, COLLECTIONS.USERS, id);
    const preparedData = prepareForFirestore(userData);
    await updateDoc(userRef, preparedData);
    
    const updatedUser = await this.getUser(id);
    if (!updatedUser) {
      throw new Error('Failed to update user');
    }
    
    return updatedUser;
  }
}

export const firestoreStorage = new FirestoreStorage();

# Production Environment Configuration
# Copy this file to .env and fill in your production Firebase credentials

# Client-side Firebase Configuration (for Vite)
VITE_FIREBASE_API_KEY=your_production_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_production_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_production_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_production_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_production_messaging_sender_id
VITE_FIREBASE_APP_ID=your_production_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_production_measurement_id

# Server-side Firebase Configuration
FIREBASE_PROJECT_ID=your_production_project_id
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your_production_project_id",...}

# Application Configuration
NODE_ENV=production

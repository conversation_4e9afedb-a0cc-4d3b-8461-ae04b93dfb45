import { db } from '@shared/firebase-config';
import { firestoreStorage } from './firestore-storage';

// Check for required Firebase environment variables
const requiredEnvVars = [
  'FIREBASE_PROJECT_ID',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0 && process.env.NODE_ENV !== 'development') {
  throw new Error(
    `Missing required Firebase environment variables: ${missingEnvVars.join(', ')}`
  );
}

// Export Firestore database instance and storage
export { db };
export const storage = firestoreStorage;
import admin from 'firebase-admin';

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY 
    ? JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)
    : undefined;

  if (serviceAccount) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID,
    });
  } else if (process.env.NODE_ENV === 'development') {
    // For development, you can use the Firebase emulator
    admin.initializeApp({
      projectId: process.env.FIREBASE_PROJECT_ID || 'demo-churchcare',
    });
  } else {
    throw new Error('Firebase service account key is required for production');
  }
}

export const adminAuth = admin.auth();
export const adminDb = admin.firestore();

export default admin;
